<template>
  <div class="about-page">
    <div class="central central-800">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">关于我们</h1>
        <p class="page-subtitle">两个人的美好时光</p>
      </div>

      <el-skeleton :loading="loading" animated>
        <template #default>
          <!-- 简化的关于信息 -->
          <div class="about-content">
            <el-timeline>
              <!-- 相遇故事 -->
              <el-timeline-item
                timestamp="2023年12月29日"
                placement="top"
                type="primary"
                size="large"
              >
                <el-card class="timeline-card" shadow="hover">
                  <div class="card-content">
                    <div class="card-header">
                      <div class="card-icon primary-icon">
                        <el-icon :size="24"><Calendar /></el-icon>
                      </div>
                      <h2 class="card-title">我们的故事</h2>
                    </div>
                    <p class="card-description">
                      这里记录着我们的美好时光，从相遇的那一刻开始，生活变得更加精彩。
                      每一个平凡的日子都因为有了彼此而变得特别。
                    </p>
                    <div class="card-footer">
                      <el-tag type="primary" size="small" round>
                        <el-icon style="margin-right: 4px"><Calendar /></el-icon>
                        相遇纪念
                      </el-tag>
                    </div>
                  </div>
                </el-card>
              </el-timeline-item>

              <!-- 男生介绍 -->
              <el-timeline-item
                timestamp="认识他"
                placement="top"
                type="success"
                size="large"
              >
                <el-card class="timeline-card" shadow="hover">
                  <div class="card-content">
                    <div class="card-header">
                      <div class="card-icon success-icon">
                        <el-icon :size="24"><User /></el-icon>
                      </div>
                      <h2 class="card-title">遇见他</h2>
                    </div>
                    <div class="person-profile">
                      <div class="person-avatar-section">
                        <el-avatar :size="80" class="person-avatar">
                          <el-icon :size="32"><User /></el-icon>
                        </el-avatar>
                        <div class="person-basic-info">
                          <h3 class="person-name">张家伟</h3>
                          <span class="person-nickname boy-nickname">布布</span>
                        </div>
                      </div>
                      <p class="person-description">
                        一个温暖阳光的大男孩，总是用他的细心和耐心照顾着身边的人。
                        技术宅的外表下藏着一颗浪漫的心。
                      </p>
                      <div class="person-traits">
                        <el-tag type="success" size="small" round>技术宅</el-tag>
                        <el-tag type="success" size="small" round>温暖</el-tag>
                        <el-tag type="success" size="small" round>细心</el-tag>
                        <el-tag type="success" size="small" round>幽默</el-tag>
                      </div>
                    </div>
                  </div>
                </el-card>
              </el-timeline-item>

              <!-- 女生介绍 -->
              <el-timeline-item
                timestamp="认识她"
                placement="top"
                type="danger"
                size="large"
              >
                <el-card class="timeline-card" shadow="hover">
                  <div class="card-content">
                    <div class="card-header">
                      <div class="card-icon danger-icon">
                        <el-icon :size="24"><User /></el-icon>
                      </div>
                      <h2 class="card-title">遇见她</h2>
                    </div>
                    <div class="person-profile">
                      <div class="person-avatar-section">
                        <el-avatar :size="80" class="person-avatar">
                          <el-icon :size="32"><User /></el-icon>
                        </el-avatar>
                        <div class="person-basic-info">
                          <h3 class="person-name">毛双欢</h3>
                          <span class="person-nickname girl-nickname">一二</span>
                        </div>
                      </div>
                      <p class="person-description">
                        一个可爱活泼的女孩子，总是能够用她的笑容温暖周围的每一个人。
                        浪漫而感性，让生活充满惊喜。
                      </p>
                      <div class="person-traits">
                        <el-tag type="danger" size="small" round>可爱</el-tag>
                        <el-tag type="danger" size="small" round>活泼</el-tag>
                        <el-tag type="danger" size="small" round>浪漫</el-tag>
                        <el-tag type="danger" size="small" round>感性</el-tag>
                      </div>
                    </div>
                  </div>
                </el-card>
              </el-timeline-item>
            </el-timeline>
          </div>
        </template>
      </el-skeleton>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Calendar, User } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(true)

// 模拟数据加载
onMounted(async () => {
  // 模拟加载延迟
  setTimeout(() => {
    loading.value = false
  }, 1000)
})
</script>

<style scoped>
.about-page {
  padding: 2rem 0;
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 3rem;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #986fee, #8695e6, #68b7dd, #18d7d3);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.page-subtitle {
  font-size: 1.2rem;
  color: #666;
  font-weight: 400;
}

/* 关于内容区域 */
.about-content {
  max-width: 900px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* 时间线卡片样式 */
.timeline-card {
  border-radius: 1rem;
  border: 1px solid rgba(208, 206, 206, 0.3);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.timeline-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

/* 卡片内容 */
.card-content {
  padding: 2rem;
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.card-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 0.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  flex-shrink: 0;
}

.primary-icon {
  background: linear-gradient(135deg, #409eff, #79bbff);
}

.success-icon {
  background: linear-gradient(135deg, #67c23a, #95d475);
}

.danger-icon {
  background: linear-gradient(135deg, #f56c6c, #f89898);
}

.card-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
  margin: 0;
}

.card-description {
  font-size: 1rem;
  line-height: 1.8;
  color: #666;
  margin-bottom: 1.5rem;
  text-align: justify;
}

.card-footer {
  display: flex;
  justify-content: flex-start;
}

/* 人物介绍专用样式 */
.person-profile {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.person-avatar-section {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.person-avatar {
  border: 3px solid #67c23a;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.person-avatar:hover {
  transform: scale(1.05);
}

.person-basic-info {
  flex: 1;
}

.person-name {
  font-size: 1.3rem;
  font-weight: 700;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.person-nickname {
  font-size: 0.9rem;
  color: #fff;
  padding: 0.3rem 0.8rem;
  border-radius: 1rem;
  display: inline-block;
  font-weight: 500;
}

.boy-nickname {
  background: linear-gradient(135deg, #67c23a, #95d475);
}

.girl-nickname {
  background: linear-gradient(135deg, #f56c6c, #f89898);
}

.person-description {
  font-size: 1rem;
  line-height: 1.8;
  color: #666;
  text-align: justify;
}

.person-traits {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-title {
    font-size: 2rem;
  }

  .page-subtitle {
    font-size: 1rem;
  }

  .about-content {
    padding: 0 1rem;
  }

  .card-content {
    padding: 1.5rem;
  }

  .person-avatar-section {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .card-header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 1.8rem;
  }

  .about-content {
    padding: 0 0.5rem;
  }

  .card-content {
    padding: 1.2rem;
  }

  .card-title {
    font-size: 1.3rem;
  }

  .person-name {
    font-size: 1.2rem;
  }

  .card-icon {
    width: 2.5rem;
    height: 2.5rem;
  }
}
</style>
