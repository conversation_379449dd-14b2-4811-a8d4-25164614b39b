<template>
  <div class="article-card"
       :class="{ 'animate__animated animate__fadeInUp': animate }"
       :style="{ animationDelay: `${index * 0.1}s` }" 
       @click="handleClick" 
       @keydown="handleKeydown" 
       tabindex="0"
       role="button" 
       :aria-label="`阅读文章：${article.title}`">
    <!-- 文章内容 -->
    <div class="article-content">
      <!-- 文章头部 -->
      <div class="article-header">
        <h3 class="article-title" :title="article.title">
          {{ article.title }}
        </h3>
      </div>

      <!-- 文章元信息 -->
      <div class="article-meta">
        <div class="meta-item author">
          <el-icon>
            <User />
          </el-icon>
          <span>{{ article.author }}</span>
        </div>
        <div class="meta-item date">
          <el-icon>
            <Calendar />
          </el-icon>
          <time :datetime="article.created_time">{{ formatDate(article.created_time) }}</time>
        </div>
      </div>

      <!-- 文章预览内容 -->
      <div class="article-preview">
        <p>{{ getPreviewContent(article.content) }}</p>
      </div>

      <!-- 文章标签 -->
      <div v-if="article.tags && article.tags.length > 0" class="article-tags">
        <el-tag v-for="tag in article.tags.slice(0, 3)" :key="tag" size="small" effect="plain"
          class="tag-item">
          {{ tag }}
        </el-tag>
        <span v-if="article.tags.length > 3" class="more-tags">
          +{{ article.tags.length - 3 }}
        </span>
      </div>

      <!-- 文章底部信息 -->
      <div class="article-footer">
        <div v-if="article.category" class="category">
          <el-icon>
            <Folder />
          </el-icon>
          <span>{{ getCategoryLabel(article.category) }}</span>
        </div>
        <div class="read-more">
          <span>阅读全文</span>
          <el-icon>
            <ArrowRight />
          </el-icon>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ArrowRight, User, Calendar, Folder } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

const props = defineProps({
  article: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    default: 0
  },
  animate: {
    type: Boolean,
    default: false
  },
  viewMode: {
    type: String,
    default: 'grid'
  }
})

const emit = defineEmits(['click'])

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  return dayjs(dateStr).format('YYYY年MM月DD日')
}

// 获取文章预览内容
const getPreviewContent = (content) => {
  if (!content) return '暂无内容预览...'

  // 移除HTML标签，获取纯文本
  const text = content.replace(/<[^>]*>/g, '').trim()
  // 调整预览长度
  const maxLength = 120
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}

// 获取分类标签
const getCategoryLabel = (category) => {
  const categoryMap = {
    'sweet-moment': '甜蜜时光',
    'daily-life': '日常生活',
    'travel-together': '一起旅行',
    'food-story': '美食故事',
    'anniversary': '纪念日',
    'future-plan': '未来计划'
  }
  return categoryMap[category] || category
}

// 点击处理
const handleClick = () => {
  emit('click', props.article.id)
}

// 键盘导航处理
const handleKeydown = (event) => {
  if (event.key === 'Enter' || event.key === ' ') {
    event.preventDefault()
    handleClick()
  }
}
</script>

<style scoped>
.article-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: var(--radius-large);
  overflow: hidden;
  box-shadow: var(--shadow-light);
  backdrop-filter: var(--backdrop-blur-lg);
  border: 1px solid var(--border-light);
  transition: all var(--transition-normal);
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.article-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.article-card:focus-visible {
  outline: 2px solid var(--el-color-primary);
  outline-offset: 4px;
}

/* 文章内容 */
.article-content {
  padding: var(--spacing-lg);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.article-header {
  margin-bottom: var(--spacing-md);
}

.article-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
  line-height: var(--line-height-tight);
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.article-tag {
  display: inline-block;
  font-size: var(--font-size-xs);
  padding: 2px 6px;
  border-radius: var(--radius-small);
  margin-left: var(--spacing-sm);
  font-weight: var(--font-weight-normal);
}

/* 文章元信息 */
.article-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-sm);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-secondary);
}

.meta-item .el-icon {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

/* 文章预览 */
.article-preview {
  margin-bottom: var(--spacing-md);
  flex: 1;
}

.article-preview p {
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  font-size: var(--font-size-sm);
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
}

/* 文章标签 */
.article-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  align-items: center;
}

.tag-item {
  background: linear-gradient(135deg, #667eea20, #764ba220);
  border-color: transparent;
  color: var(--text-primary);
}

.more-tags {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  background: rgba(255, 255, 255, 0.5);
  padding: 2px 6px;
  border-radius: var(--radius-small);
}

/* 文章底部 */
.article-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: var(--spacing-md);
  border-top: 1px solid rgba(255, 255, 255, 0.3);
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.category {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  background: linear-gradient(135deg, #667eea15, #764ba215);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-medium);
}

.read-more {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: #667eea;
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-normal);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-medium);
  background: linear-gradient(135deg, #667eea10, #764ba210);
}

.article-card:hover .read-more {
  transform: translateX(3px);
  background: linear-gradient(135deg, #667eea20, #764ba220);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .article-content {
    padding: var(--spacing-md);
  }

  .article-title {
    font-size: var(--font-size-base);
  }

  .article-meta {
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
  }

  .article-preview p {
    -webkit-line-clamp: 2;
    line-clamp: 2;
  }
}

@media (max-width: 480px) {
  .article-meta {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .article-card:hover {
    transform: translateY(-3px);
  }

  .article-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
}

/* 动画效果 */
.animate__fadeInUp {
  animation-duration: 0.6s;
  animation-fill-mode: both;
  animation-name: fadeInUp;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 30px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
</style>